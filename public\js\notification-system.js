/**
 * Naroop Centralized Notification System
 * Unified notification and error handling for consistent UX
 */

class NotificationSystem {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.defaultDuration = 5000;
        this.maxNotifications = 5;
        this.init();
    }

    init() {
        this.createContainer();
        this.setupStyles();
    }

    createContainer() {
        // Remove existing container if it exists
        const existing = document.getElementById('notification-container');
        if (existing) {
            existing.remove();
        }

        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'notification-container';
        this.container.setAttribute('aria-live', 'polite');
        this.container.setAttribute('aria-atomic', 'false');
        
        document.body.appendChild(this.container);
    }

    setupStyles() {
        // Check if styles already exist
        if (document.getElementById('notification-system-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'notification-system-styles';
        style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
                max-width: 400px;
            }

            .notification {
                background: var(--card-background, #333);
                color: var(--text-primary, #fff);
                border-radius: var(--border-radius-md, 8px);
                padding: var(--spacing-md, 16px);
                margin-bottom: var(--spacing-sm, 8px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                border-left: 4px solid var(--primary-color, #ff6b35);
                pointer-events: auto;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                max-width: 100%;
                word-wrap: break-word;
            }

            .notification.show {
                transform: translateX(0);
                opacity: 1;
            }

            .notification.success {
                border-left-color: var(--success-color, #4caf50);
            }

            .notification.error {
                border-left-color: var(--error-color, #f44336);
            }

            .notification.warning {
                border-left-color: var(--warning-color, #ff9800);
            }

            .notification.info {
                border-left-color: var(--info-color, #2196f3);
            }

            .notification-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: var(--spacing-xs, 4px);
            }

            .notification-icon {
                font-size: 1.2em;
                margin-right: var(--spacing-sm, 8px);
            }

            .notification-title {
                font-weight: 600;
                font-size: 0.9em;
                flex: 1;
            }

            .notification-close {
                background: none;
                border: none;
                color: inherit;
                cursor: pointer;
                font-size: 1.2em;
                padding: 0;
                margin-left: var(--spacing-sm, 8px);
                opacity: 0.7;
                transition: opacity 0.2s;
            }

            .notification-close:hover {
                opacity: 1;
            }

            .notification-message {
                font-size: 0.85em;
                line-height: 1.4;
                opacity: 0.9;
            }

            .notification-actions {
                margin-top: var(--spacing-sm, 8px);
                display: flex;
                gap: var(--spacing-xs, 4px);
            }

            .notification-action {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: inherit;
                padding: var(--spacing-xs, 4px) var(--spacing-sm, 8px);
                border-radius: var(--border-radius-sm, 4px);
                font-size: 0.8em;
                cursor: pointer;
                transition: background-color 0.2s;
            }

            .notification-action:hover {
                background: rgba(255, 255, 255, 0.2);
            }

            @media (max-width: 480px) {
                .notification-container {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                    max-width: none;
                }
            }
        `;
        
        document.head.appendChild(style);
    }

    /**
     * Show a notification
     * @param {string} message - Notification message
     * @param {Object} options - Notification options
     */
    show(message, options = {}) {
        const {
            type = 'info',
            title = '',
            duration = this.defaultDuration,
            persistent = false,
            actions = [],
            icon = this.getDefaultIcon(type)
        } = options;

        // Limit number of notifications
        if (this.notifications.size >= this.maxNotifications) {
            const oldestId = this.notifications.keys().next().value;
            this.hide(oldestId);
        }

        const id = this.generateId();
        const notification = this.createNotification(id, message, {
            type, title, duration, persistent, actions, icon
        });

        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // Trigger animation
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });

        // Auto-hide if not persistent
        if (!persistent && duration > 0) {
            setTimeout(() => {
                this.hide(id);
            }, duration);
        }

        return id;
    }

    createNotification(id, message, options) {
        const { type, title, actions, icon } = options;
        
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.setAttribute('data-notification-id', id);
        notification.setAttribute('role', 'alert');

        const hasTitle = title && title.trim();
        const hasActions = actions && actions.length > 0;

        notification.innerHTML = `
            ${hasTitle ? `
                <div class="notification-header">
                    <div class="notification-title">
                        ${icon ? `<span class="notification-icon">${icon}</span>` : ''}
                        ${title}
                    </div>
                    <button class="notification-close" aria-label="Close notification">&times;</button>
                </div>
            ` : `
                <div class="notification-header">
                    <div class="notification-title">
                        ${icon ? `<span class="notification-icon">${icon}</span>` : ''}
                    </div>
                    <button class="notification-close" aria-label="Close notification">&times;</button>
                </div>
            `}
            <div class="notification-message">${message}</div>
            ${hasActions ? `
                <div class="notification-actions">
                    ${actions.map(action => `
                        <button class="notification-action" data-action="${action.id}">
                            ${action.label}
                        </button>
                    `).join('')}
                </div>
            ` : ''}
        `;

        // Add event listeners
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => this.hide(id));

        // Add action listeners
        if (hasActions) {
            const actionBtns = notification.querySelectorAll('.notification-action');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const actionId = e.target.getAttribute('data-action');
                    const action = actions.find(a => a.id === actionId);
                    if (action && action.handler) {
                        action.handler();
                    }
                    if (action && action.closeOnClick !== false) {
                        this.hide(id);
                    }
                });
            });
        }

        return notification;
    }

    /**
     * Hide a notification
     * @param {string} id - Notification ID
     */
    hide(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        notification.classList.remove('show');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 300);
    }

    /**
     * Clear all notifications
     */
    clear() {
        this.notifications.forEach((notification, id) => {
            this.hide(id);
        });
    }

    // Convenience methods
    success(message, options = {}) {
        return this.show(message, { ...options, type: 'success' });
    }

    error(message, options = {}) {
        return this.show(message, { ...options, type: 'error', duration: 8000 });
    }

    warning(message, options = {}) {
        return this.show(message, { ...options, type: 'warning', duration: 6000 });
    }

    info(message, options = {}) {
        return this.show(message, { ...options, type: 'info' });
    }

    // Utility methods
    getDefaultIcon(type) {
        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };
        return icons[type] || '';
    }

    generateId() {
        return 'notification-' + Math.random().toString(36).substr(2, 9);
    }
}

// Create singleton instance
const notificationSystem = new NotificationSystem();

// Export for use in other modules
export { NotificationSystem, notificationSystem };
export default notificationSystem;
