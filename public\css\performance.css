/* Naroop Performance Optimization Styles */
/* Consolidated performance optimizations for all devices */

/* Critical rendering path optimization */
.critical-content {
    display: block;
}

.non-critical-content {
    display: none;
}

.non-critical-content.loaded {
    display: block;
}

/* Lazy loading styles */
.lazy-load {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.lazy-load.loaded {
    opacity: 1;
}

.lazy-load-placeholder {
    background: var(--surface-color);
    border-radius: var(--border-radius-md);
    position: relative;
    overflow: hidden;
}

.lazy-load-placeholder::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Image optimization */
.optimized-image {
    max-width: 100%;
    height: auto;
    object-fit: cover;
    transition: opacity var(--transition-fast);
}

.optimized-image[data-loading="true"] {
    opacity: 0.5;
}

/* Content delivery optimization */
.cdn-asset {
    /* Styles for CDN-delivered assets */
    will-change: transform;
}

/* Performance monitoring indicators */
.performance-indicator {
    position: fixed;
    top: 10px;
    right: 10px;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xs);
    font-size: var(--font-size-xs);
    z-index: var(--z-tooltip);
    opacity: 0.8;
}

.performance-indicator.good {
    border-color: var(--success-color);
}

.performance-indicator.warning {
    border-color: var(--warning-color);
}

.performance-indicator.poor {
    border-color: var(--error-color);
}

/* Reduce layout thrashing */
.layout-stable {
    contain: layout style;
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* Optimize font loading */
@font-face {
    font-family: 'Inter';
    font-display: swap;
    src: local('Inter'), url('/fonts/inter.woff2') format('woff2');
}

/* Critical CSS inlining helper */
.above-fold {
    /* Styles for above-the-fold content */
    priority: high;
}

.below-fold {
    /* Styles for below-the-fold content */
    priority: low;
}

/* Resource hints optimization */
.preload-hint {
    /* For resources that should be preloaded */
    importance: high;
}

.prefetch-hint {
    /* For resources that should be prefetched */
    importance: low;
}

/* Memory usage optimization */
.memory-efficient {
    /* Reduce memory footprint */
    contain: strict;
}

/* Network optimization */
.network-efficient {
    /* Optimize for slow networks */
    content-visibility: auto;
    contain-intrinsic-size: 200px;
}

/* Performance debugging */
.perf-debug {
    outline: 2px solid red;
    background: rgba(255, 0, 0, 0.1);
}

.perf-debug::before {
    content: 'PERF DEBUG';
    position: absolute;
    top: 0;
    left: 0;
    background: red;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    z-index: 9999;
}

/* ============================================================================ */
/* MOBILE PERFORMANCE OPTIMIZATIONS */
/* ============================================================================ */

/* Touch optimization */
.touch-optimized {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Scroll performance */
.smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

.momentum-scroll {
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
}

/* Reduce repaints on mobile */
.mobile-optimized {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Battery-friendly animations */
@media (max-width: 768px) {
    .battery-friendly {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }

    .reduce-motion {
        animation: none !important;
        transition: none !important;
    }
}

/* Network-aware loading */
.slow-network .heavy-content {
    display: none;
}

.slow-network .light-content {
    display: block;
}

/* Mobile-specific layout optimizations */
@media (max-width: 768px) {
    .mobile-stack {
        flex-direction: column !important;
    }

    .mobile-hide {
        display: none !important;
    }

    .mobile-full-width {
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .mobile-center {
        text-align: center !important;
    }

    .mobile-padding {
        padding: var(--spacing-sm) !important;
    }

    .mobile-margin {
        margin: var(--spacing-sm) !important;
    }
}

/* Viewport optimization */
@media (max-width: 480px) {
    .viewport-optimized {
        max-width: 100vw;
        overflow-x: hidden;
    }
}

/* Memory management for mobile */
.mobile-memory-efficient {
    contain: layout style paint;
    content-visibility: auto;
    contain-intrinsic-size: 100px;
}

/* Mobile-specific font optimization */
@media (max-width: 768px) {
    .mobile-font-size {
        font-size: 16px !important; /* Prevent zoom on iOS */
    }

    .mobile-line-height {
        line-height: 1.4 !important;
    }
}

/* Gesture optimization */
.swipe-enabled {
    touch-action: pan-x pan-y;
}

.pinch-zoom-disabled {
    touch-action: manipulation;
}

/* Mobile loading states - consolidated with existing shimmer */
.mobile-loading {
    background: var(--surface-color);
    border-radius: var(--border-radius-sm);
    height: 60px;
    position: relative;
    overflow: hidden;
}

.mobile-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shimmer 1.5s infinite; /* Use existing shimmer animation */
}

/* Reduce complexity on mobile */
@media (max-width: 768px) {
    .mobile-simple {
        box-shadow: none !important;
        border-radius: var(--border-radius-sm) !important;
        background: var(--card-background) !important;
    }

    .mobile-flat {
        background: transparent !important;
        border: 1px solid var(--border-color) !important;
        box-shadow: none !important;
    }
}

/* Connection-aware optimizations */
@media (max-width: 768px) {
    .slow-connection .high-quality {
        display: none;
    }

    .slow-connection .low-quality {
        display: block;
    }

    .fast-connection .high-quality {
        display: block;
    }

    .fast-connection .low-quality {
        display: none;
    }
}